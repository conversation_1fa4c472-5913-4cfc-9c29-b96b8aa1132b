<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dhr-starter</artifactId>
        <groupId>com.deloitte.dhr</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>dhr-log-starter</artifactId>

    <packaging>jar</packaging>

    <dependencies>

        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
        </dependency>

        <!-- 解决 spring-webmvc 安全漏洞 CVE-2024-38816, CVE-2025-41242, CVE-2024-38828 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${spring-webmvc.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <scope>compile</scope>
            <exclusions>
                <!-- 排除易受攻击的spring依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <!--Web 依赖，但排除默认的日志配置-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <!-- 排除默认的 logging starter，因为我们使用自定义 logback 配置 -->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <!-- 排除易受攻击的tomcat依赖 -->
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                <!-- 排除易受攻击的spring依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
            </exclusions>
        </dependency>

        <!-- SLF4J 桥接器：将其他日志框架桥接到 SLF4J/Logback -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jul-to-slf4j</artifactId>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
        </dependency>

        <!-- 解决 tomcat 安全漏洞 CVE-2024-52316, CVE-2025-24813, CVE-2024-56337, CVE-2024-50379, CVE-2024-38286, CVE-2024-24549, CVE-2024-34750, CVE-2024-52317, CVE-2024-23672 -->
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
