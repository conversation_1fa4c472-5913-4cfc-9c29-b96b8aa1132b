<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dhr-starter</artifactId>
        <groupId>com.deloitte.dhr</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>dhr-redis-starter</artifactId>

    <packaging>jar</packaging>

    <dependencies>
        <!-- Spring Boot -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <!-- 排除易受攻击的spring相关依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
            <exclusions>
                <!-- 确保排除传递的spring-context依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
            </exclusions>
        </dependency>

        <!-- Spring Cloud -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <scope>provided</scope>
            <exclusions>
                <!-- 排除可能传递的spring-context依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
            </exclusions>
        </dependency>

        <!-- Redisson -->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>${redisson.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除可能传递的其他spring依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
            </exclusions>
        </dependency>

        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>org.xmlunit</artifactId>
                    <groupId>xmlunit-core</groupId>
                </exclusion>
                <!-- 排除旧版本json-path依赖，解决CVE-2023-51074安全漏洞 -->
                <exclusion>
                    <groupId>com.jayway.jsonpath</groupId>
                    <artifactId>json-path</artifactId>
                </exclusion>
                <!-- 排除可能传递的spring-context依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
        </dependency>

        <!-- 解决 logback 安全漏洞 -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
