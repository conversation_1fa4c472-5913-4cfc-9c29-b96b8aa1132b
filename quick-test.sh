#!/bin/bash

# 快速测试关键服务启动
echo "=== 快速服务启动测试 ==="

# 设置环境变量
export config_nacos_serveraddr=localhost
export config_nacos_port=8848
export config_nacos_namespace=d55614cb-2190-4854-b324-c2b636ddc3e8
export config_profile=test

# JVM参数
JVM_ARGS="-Dotel.javaagent.enabled=false -Dotel.sdk.disabled=true"

# 检查Nacos
echo "检查Nacos服务..."
if ! lsof -i :8848 > /dev/null 2>&1; then
    echo "❌ Nacos服务未运行"
    exit 1
fi
echo "✅ Nacos服务正在运行"

# 创建结果目录
mkdir -p test-results

# 测试函数
test_service() {
    local service=$1
    local port=$2

    echo ""
    echo "🔍 测试: $service (端口: $port)"

    # 检查端口
    if lsof -i :$port > /dev/null 2>&1; then
        echo "⚠️  端口 $port 已被占用"
        return 1
    fi

    # 设置端口
    export config_server_port=$port

    # 编译测试
    echo "   📦 编译..."
    if mvn compile -pl "dhr-service/$service" -DskipTests -q > "test-results/${service}.compile.log" 2>&1; then
        echo "   ✅ 编译成功"
    else
        echo "   ❌ 编译失败"
        echo "   查看日志: test-results/${service}.compile.log"
        return 1
    fi

    # 启动测试（5秒快速测试）
    echo "   🚀 启动测试..."
    timeout 5s mvn spring-boot:run \
        -pl "dhr-service/$service" \
        -Dspring-boot.run.jvmArguments="$JVM_ARGS" \
        -Dspring-boot.run.profiles=test \
        > "test-results/${service}.startup.log" 2>&1

    local exit_code=$?

    if [ $exit_code -eq 124 ]; then
        echo "   ⏰ 启动超时（可能正在启动）"
        return 2
    elif [ $exit_code -eq 0 ]; then
        echo "   ✅ 启动成功"
        return 0
    else
        echo "   ❌ 启动失败"
        echo "   查看日志: test-results/${service}.startup.log"
        return 1
    fi
}

# 测试关键服务
echo ""
echo "开始测试关键服务..."

# 1. XXL Job Admin (不依赖Nacos)
test_service "dhr-xxl-job-admin-service" "9080"
xxl_result=$?

# 2. Collection Service (使用provider模块)
test_service "dhr-collection-service/dhr-collection-provider" "9090"
collection_result=$?

# 3. OAuth Service
test_service "dhr-oauth-service" "9021"
oauth_result=$?

# 4. Gateway Service
test_service "dhr-gateway-service" "9020"
gateway_result=$?

# 5. Basic Service
test_service "dhr-basic-service" "9001"
basic_result=$?

# 生成报告
echo ""
echo "=== 测试报告 ==="
echo ""

report_service() {
    local service=$1
    local result=$2

    case $result in
        0) echo "✅ $service - 启动成功" ;;
        1) echo "❌ $service - 启动失败" ;;
        2) echo "⏰ $service - 启动超时" ;;
        *) echo "❓ $service - 未知状态" ;;
    esac
}

report_service "dhr-xxl-job-admin-service" $xxl_result
report_service "dhr-collection-provider" $collection_result
report_service "dhr-oauth-service" $oauth_result
report_service "dhr-gateway-service" $gateway_result
report_service "dhr-basic-service" $basic_result

echo ""
echo "📁 详细日志在 test-results/ 目录"
echo ""
echo "=== 测试完成 ==="
