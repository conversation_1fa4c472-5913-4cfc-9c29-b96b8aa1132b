<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.deloitte.dhr</groupId>
    <artifactId>dhr-business-service</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <modules>
        <module>dhr-starter</module>
        <module>dhr-common</module>
        <module>dhr-service</module>
    </modules>

    <properties>
        <!-- DHR 版本 -->
        <revision>2.3.0-SNAPSHOT</revision>
        <!-- 基础环境 -->
        <java.version>17</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <bcprov-jdk15to18.version>1.78.1</bcprov-jdk15to18.version>
        <!-- Spring Projects -->
        <spring-boot.version>3.2.4</spring-boot.version>
        <spring-cloud.version>2023.0.1</spring-cloud.version>
        <spring-cloud-alibaba.version>2023.0.1.0</spring-cloud-alibaba.version>
        <bootstrap.version>4.1.1</bootstrap.version>
        <openfeign.version>4.1.1</openfeign.version>
        <!-- POI -->
        <poi.version>5.4.1</poi.version>
        <easyexcel.version>3.1.1</easyexcel.version>
        <!-- db相关 -->
        <mybatis-plus.version>3.5.9</mybatis-plus.version>
        <druid.version>1.2.25</druid.version>
        <mapper-spring-boot-starter.version>4.3.0</mapper-spring-boot-starter.version>
        <mapper-base.version>4.3.0</mapper-base.version>
        <mybatis.version>3.5.16</mybatis.version>
        <mybatis-spring-boot-starter.version>3.0.4</mybatis-spring-boot-starter.version>
        <mybatis-spring-boot.version>3.0.4</mybatis-spring-boot.version>
        <mysql-connector-java.version>8.0.33</mysql-connector-java.version>
        <mysql-connector-j.version>8.4.0</mysql-connector-j.version>
        <!-- 兼容子模块版本配置 -->
        <spring-boot.mybatis>3.0.3</spring-boot.mybatis>
        <pagehelper.boot.version>2.1.0</pagehelper.boot.version>
        <!-- 兼容不同命名规范的版本属性 -->
        <commons-io.version>2.20.0</commons-io.version>
        <commons.io.version>2.20.0</commons.io.version>
        <commons-fileupload.version>1.5</commons-fileupload.version>
        <commons.fileupload.version>1.5</commons.fileupload.version>
        <!-- Tools -->
        <hutool.version>5.8.37</hutool.version>
        <fastjson2.version>2.0.55</fastjson2.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <!-- redis -->
        <redisson.version>3.37.0</redisson.version>
        <!-- elasticsearch -->
        <elasticsearch.version>7.17.29</elasticsearch.version>
        <!-- 阿里服务 -->
        <alipay-sdk.version>4.13.0.ALL</alipay-sdk.version>
        <aliyun-java-sdk-core.version>4.5.16</aliyun-java-sdk-core.version>
        <aliyun-java-sdk-sms.version>3.0.0-rc1</aliyun-java-sdk-sms.version>
        <aliyun-sdk-oss.version>3.10.2</aliyun-sdk-oss.version>
        <!-- 服务工具 -->
        <xxl-job.version>2.4.2</xxl-job.version>
        <!-- 测试 -->
        <powermock.version>2.0.2</powermock.version>
        <!-- 认证相关 - Spring Boot 3.x 兼容版本 -->
        <java-jwt.version>4.4.0</java-jwt.version>
        <!-- Spring Security OAuth2 在 Spring Boot 3.x 中已经集成到 spring-boot-starter-oauth2-resource-server -->
        <!-- <spring-security-oauth2.version>2.5.2.RELEASE</spring-security-oauth2.version> -->
        <!-- <spring-security-jwt.version>1.0.10.RELEASE</spring-security-jwt.version> -->
        <!-- webService -->
        <cxf-rt.version>3.1.12</cxf-rt.version>

        <!-- pdf -->
        <freemarker.version>2.3.31</freemarker.version>
        <!--activiti 6-->
        <activiti.version>6.0.0</activiti.version>
        <activiti-bpmn.version>6.0.0</activiti-bpmn.version>
        <!-- 其他 -->
        <sap.sapjco3.version>3.0.11</sap.sapjco3.version>
        <velocity.version>2.4.1</velocity.version>
        <!-- Jakarta EE 版本配置 - Spring Boot 3.x 兼容 -->
        <jakarta.interceptor-api.version>2.1.0</jakarta.interceptor-api.version>
        <jakarta.servlet-api.version>6.0.0</jakarta.servlet-api.version>
        <!-- 兼容旧版本 -->
        <javax.interceptor-api.version>1.2</javax.interceptor-api.version>
        <javax.servlet-api.version>4.0.1</javax.servlet-api.version>
        <!-- 旧版本servlet-api已废弃 -->
        <!-- <servlet-api.version>2.5</servlet-api.version> -->
        <logstash-logback-encoder.version>7.2</logstash-logback-encoder.version>
        <jettison.version>1.5.4</jettison.version>
        <UserAgentUtils.version>1.21</UserAgentUtils.version>
        <minio.version>8.5.17</minio.version>
        <admin.version>2.7.8</admin.version>
        <httpcore.version>4.4.14</httpcore.version>
        <httpclient.version>4.5.13</httpclient.version>
        <activation.version>1.1.1</activation.version>
        <axis2.version>1.7.9</axis2.version>
        <jaxrpc.version>1.1</jaxrpc.version>
        <mail.version>1.4.7</mail.version>
        <!-- Jakarta Mail for Spring Boot 3.x -->
        <jakarta.mail.version>2.0.1</jakarta.mail.version>
        <nacos-common.version>2.3.3</nacos-common.version>
        <pagehelper-starter.version>1.4.1</pagehelper-starter.version>
        <pagehelper.version>5.3.1</pagehelper.version>
        <jsqlparser.version>4.2</jsqlparser.version>
        <geantyref.version>1.3.11</geantyref.version>
        <commons-pool2.version>2.6.2</commons-pool2.version>
        <jjwt.version>0.9.1</jjwt.version>
        <joda-time.version>2.9.9</joda-time.version>
        <shedlock.version>4.39.0</shedlock.version>
        <disruptor.version>3.3.6</disruptor.version>
        <jasypt.version>3.0.2</jasypt.version>
        <spring-retry.version>1.2.5.RELEASE</spring-retry.version>
        <okhttp.version>4.12.0</okhttp.version>
        <aspose-words.version>22.10</aspose-words.version>
        <pdfbox.version>2.0.26</pdfbox.version>

        <!-- 通用工具库版本 -->
        <commons-lang3.version>3.18.0</commons-lang3.version>
        <jackson-core.version>2.17.2</jackson-core.version>

        <!-- Spring 6.x 版本配置 - 与Spring Boot 3.2.4兼容 -->
        <spring-beans.version>6.1.5</spring-beans.version>
        <spring-context.version>6.1.5</spring-context.version>
        <spring-expression.version>6.1.5</spring-expression.version>
        <spring-web.version>6.1.5</spring-web.version>
        <spring-webmvc.version>6.1.5</spring-webmvc.version>
        <spring-webflux.version>6.1.5</spring-webflux.version>

        <!-- 添加安全版本以解决CVE-2022-3509、CVE-2022-3510漏洞 -->
        <protobuf-java.version>4.28.2</protobuf-java.version>
        <!-- 添加xstream安全版本以解决CVE-2022-41966、CVE-2024-47072、CVE-2022-40151漏洞 -->
        <xstream.version>1.4.21</xstream.version>


        <!-- Spring Security 6.x 版本配置 - 与Spring Boot 3.2.4兼容 -->
        <spring-security.version>6.2.4</spring-security.version>
        <!-- Spring Security OAuth2 Authorization Server 版本 - 与Spring Boot 3.2.4兼容 -->
        <spring-security-oauth2-authorization-server.version>1.2.4</spring-security-oauth2-authorization-server.version>

        <!-- 添加安全版本以解决commons-fileupload相关漏洞 -->
        <commons-fileupload.version>1.5</commons-fileupload.version>
        <!-- 添加安全版本以解决bouncycastle相关漏洞 -->
        <bcprov-jdk18on.version>1.81</bcprov-jdk18on.version>
        <!-- 添加兼容Spring Boot 3.2.4的tomcat安全版本 -->
        <tomcat.version>10.1.33</tomcat.version>
        <!-- 添加安全版本以解决spring-boot-actuator相关漏洞 -->
        <spring-boot-actuator.version>3.2.4</spring-boot-actuator.version>
        <!-- Jakarta XML Bind API for Spring Boot 3 -->
        <jakarta.xml.bind-api.version>4.0.1</jakarta.xml.bind-api.version>
        <!-- JAXB 实现版本 -->
        <jaxb-impl.version>4.0.4</jaxb-impl.version>
        <jaxb-runtime.version>4.0.4</jaxb-runtime.version>
        <!-- 添加安全版本以解决netty相关漏洞 -->
        <netty.version>4.1.118.Final</netty.version>
        <netty-codec-http.version>4.1.118.Final</netty-codec-http.version>

        <!-- 添加安全版本以解决guava相关漏洞 -->
        <guava.version>33.4.0-jre</guava.version>

        <!-- 解决 logback 安全漏洞 - Spring Boot 3.x 兼容版本 -->
        <logback.version>1.4.14</logback.version>
        <!-- 显式声明不使用 log4j2 -->
        <log4j2.excluded>true</log4j2.excluded>
        <selenium.version>4.27.0</selenium.version>
        <!-- 解决 async-http-client 安全漏洞 -->
        <async.version>2.12.4</async.version>
        <!-- 解决 json-path 安全漏洞 -->
        <json-path.version>2.9.0</json-path.version>
        <!-- 解决 xmlunit-core 安全漏洞 -->
        <xmlunit.version>2.10.3</xmlunit.version>

        <!-- 添加thymeleaf安全版本以解决CVE-2023-38286漏洞 -->
        <thymeleaf.version>3.1.2.RELEASE</thymeleaf.version>
        <!-- 添加xdocreport安全版本以解决CVE-2017-9096漏洞 -->
        <xdocreport.version>2.0.6</xdocreport.version>
        <easyexcel-core.version>4.0.3</easyexcel-core.version>
        <commons-compress.version>1.28.0</commons-compress.version>
        <feign-hystrix.version>2.10.3</feign-hystrix.version>
        <alibaba-nls-sdk.version>2.2.11</alibaba-nls-sdk.version>
        <drools.version>7.73.0.Final</drools.version>

        <spring-cloud-loadbalancer.version>4.1.1</spring-cloud-loadbalancer.version>
        <spring-cloud-commons.version>4.1.1</spring-cloud-commons.version>
        <spring-cloud-starter-loadbalancer.version>4.1.1</spring-cloud-starter-loadbalancer.version>
        <spring-cloud-starter-gateway.version>4.1.1</spring-cloud-starter-gateway.version>
        <!-- Circuit Breaker 版本 -->
        <spring-cloud-circuitbreaker.version>3.1.1</spring-cloud-circuitbreaker.version>
        <resilience4j.version>1.7.1</resilience4j.version>
        <!-- 添加commons-email安全版本以解决CVE-2017-9801和CVE-2018-1294漏洞 -->
        <commons-email.version>1.6.0</commons-email.version>
    </properties>

    <dependencyManagement>

        <dependencies>
            <!-- ========================================= -->
            <!-- Spring 生态系统 BOM 依赖管理 -->
            <!-- ========================================= -->

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- ========================================= -->
            <!-- 数据库相关依赖 -->
            <!-- ========================================= -->

            <!-- MyBatis Plus Spring Boot 3 Starter -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- 兼容旧的starter名称 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- ========================================= -->
            <!-- Jakarta EE / Servlet API -->
            <!-- ========================================= -->

            <!-- 兼容旧的javax依赖 -->
            <dependency>
                <groupId>javax.interceptor</groupId>
                <artifactId>javax.interceptor-api</artifactId>
                <version>${javax.interceptor-api.version}</version>
            </dependency>

            <dependency>
                <groupId>jakarta.servlet</groupId>
                <artifactId>jakarta.servlet-api</artifactId>
                <version>${jakarta.servlet-api.version}</version>
            </dependency>

            <!-- Jakarta Mail API for Spring Boot 3.x -->
            <dependency>
                <groupId>jakarta.mail</groupId>
                <artifactId>jakarta.mail-api</artifactId>
                <version>${jakarta.mail.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>jakarta.mail</artifactId>
                <version>${jakarta.mail.version}</version>
            </dependency>
            <!-- ========================================= -->
            <!-- Spring Cloud 组件 -->
            <!-- ========================================= -->

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-bootstrap</artifactId>
                <version>${bootstrap.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${openfeign.version}</version>
            </dependency>

            <!-- Circuit Breaker 依赖 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
                <version>${spring-cloud-circuitbreaker.version}</version>
            </dependency>

            <!-- Feign Hystrix 兼容性依赖 -->
            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-hystrix</artifactId>
                <version>11.10</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper-starter.version}</version>
            </dependency>


            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>

            <!-- 其他 -->
            <dependency>
                <groupId>io.leangen.geantyref</groupId>
                <artifactId>geantyref</artifactId>
                <version>${geantyref.version}</version>
            </dependency>

            <!-- 定时任务分布数锁-->
            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-spring</artifactId>
                <version>${shedlock.version}</version>
            </dependency>

            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-provider-redis-spring</artifactId>
                <version>${shedlock.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>${spring-retry.version}</version>
            </dependency>

            <!--sentinel相关-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-actuator</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <!-- 国密算法Bouncy Castle依赖 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15to18</artifactId>
                <version>${bcprov-jdk15to18.version}</version>
            </dependency>

            <!-- SnakeYAML安全版本 -->
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>2.0</version>
            </dependency>

            <!-- POI -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-core</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- HuTool -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun-java-sdk-core.version}</version>
            </dependency>

            <!-- Spring Security OAuth2 已在 Spring Boot 3.x 中重构，使用新的 starter -->
            <!--
            <dependency>
                <groupId>org.springframework.security.oauth</groupId>
                <artifactId>spring-security-oauth2</artifactId>
                <version>${spring-security-oauth2.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-jwt</artifactId>
                <version>${spring-security-jwt.version}</version>
            </dependency>
            -->

            <!-- Jakarta EE 依赖 - Spring Boot 3.x 兼容 -->
            <dependency>
                <groupId>jakarta.interceptor</groupId>
                <artifactId>jakarta.interceptor-api</artifactId>
                <version>${jakarta.interceptor-api.version}</version>
            </dependency>

            <!-- Redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- ========================================= -->
            <!-- 日志框架统一管理 - 只使用 Logback -->
            <!-- ========================================= -->

            <!-- Logback 日志框架 -->
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <!-- Logstash 编码器用于日志结构化 -->
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash-logback-encoder.version}</version>
            </dependency>

            <!-- 桥接器：将其他日志框架桥接到 SLF4J -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>2.0.7</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jul-to-slf4j</artifactId>
                <version>2.0.7</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>2.0.7</version>
            </dependency>

            <!-- 显式排除 Log4j2 相关依赖 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-log4j2</artifactId>
                <version>${spring-boot.version}</version>
                <scope>provided</scope>
                <optional>true</optional>
                <!-- 标记为不使用 -->
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>2.20.0</version>
                <scope>provided</scope>
                <optional>true</optional>
                <!-- 标记为不使用 -->
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>2.20.0</version>
                <scope>provided</scope>
                <optional>true</optional>
                <!-- 标记为不使用 -->
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2-extension</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2-extension-spring5</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.jettison</groupId>
                <artifactId>jettison</artifactId>
                <version>${jettison.version}</version>
            </dependency>

            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${UserAgentUtils.version}</version>
            </dependency>

            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper-spring-boot-starter</artifactId>
                <version>${mapper-spring-boot-starter.version}</version>
            </dependency>
            <!-- 添加安全版本的 mybatis 依赖 -->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${httpcore.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-sdk-oss.version}</version>
            </dependency>

            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>${jsqlparser.version}</version>
            </dependency>

            <!-- Jakarta/JAXB 相关依赖 - Spring Boot 3.x 兼容 -->
            <dependency>
                <groupId>jakarta.xml.bind</groupId>
                <artifactId>jakarta.xml.bind-api</artifactId>
                <version>${jakarta.xml.bind-api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-runtime</artifactId>
                <version>${jaxb-runtime.version}</version>
            </dependency>

            <!-- 兼容旧的javax依赖 -->
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>2.3.1</version>
            </dependency>

            <dependency>
                <groupId>javax.activation</groupId>
                <artifactId>activation</artifactId>
                <version>${activation.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.xml</groupId>
                <artifactId>jaxrpc</artifactId>
                <version>${jaxrpc.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.mail</groupId>
                <artifactId>mail</artifactId>
                <version>${mail.version}</version>
            </dependency>

            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${admin.version}</version>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.version}</version>
            </dependency>

            <!-- pdf -->
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>

            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-api</artifactId>
                <version>${selenium.version}</version>
            </dependency>

            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-remote-driver</artifactId>
                <version>${selenium.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-common</artifactId>
                <version>${nacos-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- velocity模板引擎，用于mybatis-plus代码生成器 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <dependency>
                <groupId>org.activiti</groupId>
                <artifactId>activiti-spring-boot-starter-jpa</artifactId>
                <version>${activiti.version}</version>
            </dependency>

            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>${jasypt.version}</version>
            </dependency>

            <!-- dhr starter -->
            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-api-encryption-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-excel-parser-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-log-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-quartz-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-redis-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-report-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-resubmit-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-rule-starter</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- dhr common -->
            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-common-bean</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-common-util</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-common-mybtismvc</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-common-auth</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.deloitte.dhr</groupId>
                <artifactId>dhr-common-jco</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- xxl-job -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.luhuiguo</groupId>
                <artifactId>aspose-words</artifactId>
                <version>${aspose-words.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-web</artifactId>
                <version>${spring-security.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${spring-security.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-crypto</artifactId>
                <version>${spring-security.version}</version>
            </dependency>

            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${java-jwt.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>

            <!-- 显式指定安全的jackson-core版本以解决WS-2022-0468漏洞 -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson-core.version}</version>
            </dependency>

            <!-- 添加elasticsearch相关依赖定义 -->
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <!--activiti-->
            <dependency>
                <groupId>org.activiti</groupId>
                <artifactId>activiti-spring-boot-starter-basic</artifactId>
                <version>${activiti.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>activation</artifactId>
                        <groupId>javax.activation</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mybatis</artifactId>
                        <groupId>org.mybatis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- Jakarta XML Bind API 已在上面定义，此处删除重复 -->
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!--统一版本管理插件，会在install的时候替换${revision}为具体版本号，不加会导致maven识别不了install的包-->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <configuration>
                    <!--true：更新pom文件，不然无法更新module里的pom版本号，此处还有更高级的用法，具体参靠官方文档-->
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <!-- OpenRewrite Maven Plugin for Spring Boot 3 Migration -->
            <plugin>
                <groupId>org.openrewrite.maven</groupId>
                <artifactId>rewrite-maven-plugin</artifactId>
                <version>5.20.0</version>
                <configuration>
                    <activeRecipes>
                        <recipe>org.openrewrite.java.spring.boot3.UpgradeSpringBoot_3_2</recipe>
                    </activeRecipes>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.openrewrite.recipe</groupId>
                        <artifactId>rewrite-spring</artifactId>
                        <version>5.7.0</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <!-- 指定不进行过滤的文件扩展名 -->
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>ttc</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                        <nonFilteredFileExtension>jar</nonFilteredFileExtension>
                        <!-- 添加其他二进制格式 -->
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>
