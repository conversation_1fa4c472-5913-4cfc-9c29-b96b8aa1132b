# Spring Boot 3.x Exclude 配置优化指南

## 🎯 优化目标
移除在 Spring Boot 2.7 时代为安全原因添加的、在 Spring Boot 3.x 中已不再需要的 exclude 配置。

## 📋 可以移除的 Exclude 配置

### 1. Spring Framework 核心组件（完全移除）
```xml
<!-- 以下 exclusions 在 Spring Boot 3.x 中可以完全移除 -->
<exclusion>
    <groupId>org.springframework</groupId>
    <artifactId>spring-beans</artifactId>
</exclusion>
<exclusion>
    <groupId>org.springframework</groupId>
    <artifactId>spring-context</artifactId>
</exclusion>
<exclusion>
    <groupId>org.springframework</groupId>
    <artifactId>spring-expression</artifactId>
</exclusion>
<exclusion>
    <groupId>org.springframework</groupId>
    <artifactId>spring-webmvc</artifactId>
</exclusion>
<exclusion>
    <groupId>org.springframework</groupId>
    <artifactId>spring-web</artifactId>
</exclusion>
```

### 2. Spring Security 组件（可以移除）
```xml
<!-- Spring Boot 3.x 使用 Spring Security 6.x，已修复安全漏洞 -->
<exclusion>
    <groupId>org.springframework.security</groupId>
    <artifactId>spring-security-crypto</artifactId>
</exclusion>
<exclusion>
    <groupId>org.springframework.security</groupId>
    <artifactId>spring-security-core</artifactId>
</exclusion>
<exclusion>
    <groupId>org.springframework.security</groupId>
    <artifactId>spring-security-web</artifactId>
</exclusion>
<exclusion>
    <groupId>org.springframework.security</groupId>
    <artifactId>spring-security-config</artifactId>
</exclusion>
```

### 3. Tomcat 组件（可以移除）
```xml
<!-- Spring Boot 3.x 使用安全版本的 Tomcat -->
<exclusion>
    <groupId>org.apache.tomcat.embed</groupId>
    <artifactId>tomcat-embed-core</artifactId>
</exclusion>
<exclusion>
    <groupId>org.apache.tomcat.embed</groupId>
    <artifactId>tomcat-embed-websocket</artifactId>
</exclusion>
```

### 4. Logback 组件（可以移除）
```xml
<!-- Spring Boot 3.x 使用安全版本的 Logback -->
<exclusion>
    <groupId>ch.qos.logback</groupId>
    <artifactId>logback-classic</artifactId>
</exclusion>
<exclusion>
    <groupId>ch.qos.logback</groupId>
    <artifactId>logback-core</artifactId>
</exclusion>
```

## ✅ 需要保留的 Exclude 配置

### 1. BouncyCastle 版本升级
```xml
<!-- 仍需保留，用于版本升级 -->
<exclusion>
    <groupId>org.bouncycastle</groupId>
    <artifactId>bcprov-jdk15on</artifactId>
</exclusion>
```

### 2. Jakarta EE 迁移相关
```xml
<!-- 仍需保留，用于 javax -> jakarta 迁移 -->
<exclusion>
    <groupId>javax.activation</groupId>
    <artifactId>activation</artifactId>
</exclusion>
```

## 🔧 优化后的示例

### 优化前：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-validation</artifactId>
    <exclusions>
        <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </exclusion>
        <exclusion>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
        </exclusion>
        <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </exclusion>
        <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

### 优化后：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-validation</artifactId>
    <!-- Spring Boot 3.x 中不再需要排除 Spring Framework 和 Spring Security 核心组件 -->
</dependency>
```

## 📁 需要优化的文件列表

基于扫描结果，以下文件包含可优化的 exclude 配置：

1. `dhr-starter/dhr-excel-parser-starter/pom.xml`
2. `dhr-service/dhr-performance-service/dhr-performance-provider/pom.xml`
3. `dhr-common/dhr-common-util/pom.xml`
4. `dhr-service/dhr-bpm-service/bpm-service/pom.xml`
5. `dhr-service/dhr-questionnaire-service/dhr-questionnaire-common/dhr-questionnaire-common-core/pom.xml`
6. `dhr-service/dhr-performance-new-service/dhr-performance-new-provider/pom.xml`

## ⚠️ 注意事项

1. **渐进式优化**：建议分批次优化，每次优化后进行测试
2. **版本确认**：确保已升级到 Spring Boot 3.4（当前显示为 3.2.4）
3. **安全扫描**：优化后运行安全扫描工具验证
4. **测试覆盖**：确保有充分的集成测试覆盖

## 🚀 执行建议

1. **备份当前配置**
2. **分模块优化**：从 starter 模块开始
3. **逐步验证**：每个模块优化后进行编译测试
4. **安全验证**：使用 OWASP 依赖检查插件验证
