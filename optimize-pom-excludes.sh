#!/bin/bash

# Spring Boot 3.x Exclude 配置优化脚本
# 用于移除在 Spring Boot 2.7 时代为安全原因添加的、在 Spring Boot 3.x 中已不再需要的 exclude 配置

echo "🚀 开始优化 Spring Boot 3.x Exclude 配置..."

# 定义需要移除的 exclusion 模式
declare -a EXCLUSIONS_TO_REMOVE=(
    # Spring Framework 核心组件
    "spring-beans"
    "spring-context" 
    "spring-expression"
    "spring-webmvc"
    "spring-web"
    # Spring Security 组件（Spring Boot 3.x 使用 Spring Security 6.x）
    "spring-security-crypto"
    "spring-security-core"
    "spring-security-web"
    "spring-security-config"
    # Tomcat 组件（Spring Boot 3.x 使用安全版本）
    "tomcat-embed-core"
    "tomcat-embed-websocket"
    # Logback 组件（Spring Boot 3.x 使用安全版本）
    "logback-classic"
    "logback-core"
)

# 需要保留的 exclusions（不要移除）
declare -a EXCLUSIONS_TO_KEEP=(
    "bcprov-jdk15on"  # BouncyCastle 版本升级
    "activation"      # Jakarta EE 迁移
    "mybatis"        # 特定版本控制
)

# 查找所有 pom.xml 文件
find . -name "pom.xml" -type f | while read pom_file; do
    echo "📁 处理文件: $pom_file"
    
    # 创建备份
    cp "$pom_file" "$pom_file.backup"
    
    # 标记是否有修改
    modified=false
    
    # 对每个需要移除的 exclusion 进行处理
    for exclusion in "${EXCLUSIONS_TO_REMOVE[@]}"; do
        # 检查是否包含该 exclusion
        if grep -q "<artifactId>$exclusion</artifactId>" "$pom_file"; then
            echo "  ❌ 移除 exclusion: $exclusion"
            
            # 使用 sed 移除整个 exclusion 块
            # 这个正则表达式会匹配从 <exclusion> 到 </exclusion> 的整个块
            sed -i.tmp "/<exclusion>/,/<\/exclusion>/{
                /<artifactId>$exclusion<\/artifactId>/,/<\/exclusion>/d
                /<artifactId>$exclusion<\/artifactId>/{
                    N
                    /<exclusion>/d
                }
            }" "$pom_file"
            
            # 如果修改成功，标记为已修改
            if [ $? -eq 0 ]; then
                modified=true
            fi
        fi
    done
    
    # 清理空的 exclusions 块
    sed -i.tmp '/<exclusions>/,/<\/exclusions>/{
        /<exclusions>/N
        /<exclusions>\s*<\/exclusions>/d
    }' "$pom_file"
    
    # 清理临时文件
    rm -f "$pom_file.tmp"
    
    if [ "$modified" = true ]; then
        echo "  ✅ 文件已优化: $pom_file"
    else
        echo "  ℹ️  无需修改: $pom_file"
        # 如果没有修改，删除备份文件
        rm -f "$pom_file.backup"
    fi
done

echo ""
echo "🎉 优化完成！"
echo ""
echo "📋 后续步骤："
echo "1. 检查修改的文件（备份文件以 .backup 结尾）"
echo "2. 运行 'mvn clean compile' 验证编译"
echo "3. 运行 'mvn dependency:tree' 检查依赖树"
echo "4. 运行安全扫描: 'mvn org.owasp:dependency-check-maven:check'"
echo "5. 如果一切正常，删除备份文件: 'find . -name \"*.backup\" -delete'"
echo ""
echo "⚠️  注意事项："
echo "- 建议分批次优化，每次优化后进行测试"
echo "- 如果出现问题，可以使用备份文件恢复"
echo "- 确保已升级到 Spring Boot 3.4"
