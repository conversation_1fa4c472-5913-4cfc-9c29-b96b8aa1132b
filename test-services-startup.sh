#!/bin/bash

# 确保使用bash 4.0+的关联数组功能
if [ "${BASH_VERSION%%.*}" -lt 4 ]; then
    echo "错误：需要bash 4.0或更高版本来支持关联数组"
    exit 1
fi

# DHR 微服务启动测试脚本
# 作者：AI Assistant
# 日期：2025-08-25

echo "=== DHR 微服务启动测试 ==="
echo "开始测试各个服务的启动情况..."

# 设置环境变量
export config_nacos_serveraddr=localhost
export config_nacos_port=8848
export config_nacos_namespace=d55614cb-2190-4854-b324-c2b636ddc3e8
export config_profile=test

# JVM参数
JVM_ARGS="-Dotel.javaagent.enabled=false -Dotel.sdk.disabled=true"

# 检查Nacos是否运行
echo "检查Nacos服务状态..."
if ! lsof -i :8848 > /dev/null 2>&1; then
    echo "❌ Nacos服务未运行，请先启动Nacos服务"
    exit 1
fi
echo "✅ Nacos服务正在运行"

# 创建测试结果目录
mkdir -p test-results

# 服务列表和测试配置（使用简单数组）
SERVICES=(
    "dhr-xxl-job-admin-service:9080:不依赖Nacos"
    "dhr-collection-service:9090:依赖Nacos"
    "dhr-oauth-service:9021:依赖Nacos"
    "dhr-gateway-service:9020:依赖Nacos"
    "dhr-basic-service:9001:依赖Nacos"
    "dhr-mda-service:9002:依赖Nacos"
    "dhr-ssc-service:9003:依赖Nacos"
    "dhr-talent-service:9004:依赖Nacos"
    "dhr-performance-service:9005:依赖Nacos"
    "dhr-performance-new-service:9006:依赖Nacos"
    "dhr-bpm-service:9007:依赖Nacos"
    "dhr-utility-service:9008:依赖Nacos"
    "dhr-work-order-service:9009:依赖Nacos"
    "dhr-questionnaire-service:9010:依赖Nacos"
    "dhr-ai-service:9011:依赖Nacos"
)

# 测试服务启动的函数
test_service_startup() {
    local service_info=$1
    local service_name=$(echo $service_info | cut -d':' -f1)
    local port=$(echo $service_info | cut -d':' -f2)
    local dependency=$(echo $service_info | cut -d':' -f3)

    echo ""
    echo "🔍 测试服务: $service_name"
    echo "   端口: $port"
    echo "   依赖: $dependency"

    # 检查端口是否被占用
    if lsof -i :$port > /dev/null 2>&1; then
        echo "⚠️  端口 $port 已被占用，跳过测试"
        echo "SKIP: 端口占用" > "test-results/${service_name}.result"
        return 1
    fi

    # 设置服务端口
    export config_server_port=$port

    # 编译测试
    echo "   📦 编译测试..."
    if mvn compile -pl "dhr-service/$service_name" -DskipTests -q > "test-results/${service_name}.compile.log" 2>&1; then
        echo "   ✅ 编译成功"
    else
        echo "   ❌ 编译失败"
        echo "FAIL: 编译失败" > "test-results/${service_name}.result"
        return 1
    fi

    # 启动测试（快速测试，10秒超时）
    echo "   🚀 启动测试..."
    timeout 10s mvn spring-boot:run \
        -pl "dhr-service/$service_name" \
        -Dspring-boot.run.jvmArguments="$JVM_ARGS" \
        -Dspring-boot.run.profiles=test \
        > "test-results/${service_name}.startup.log" 2>&1

    local exit_code=$?

    if [ $exit_code -eq 124 ]; then
        echo "   ⏰ 启动超时（可能正在启动中）"
        echo "TIMEOUT: 启动超时" > "test-results/${service_name}.result"
    elif [ $exit_code -eq 0 ]; then
        echo "   ✅ 启动成功"
        echo "SUCCESS: 启动成功" > "test-results/${service_name}.result"
    else
        echo "   ❌ 启动失败"
        echo "FAIL: 启动失败" > "test-results/${service_name}.result"
    fi

    return $exit_code
}

# 生成测试报告
generate_report() {
    echo ""
    echo "=== 测试报告 ==="
    echo ""

    local success_count=0
    local fail_count=0
    local timeout_count=0
    local skip_count=0

    for service_info in "${SERVICES[@]}"; do
        local service_name=$(echo $service_info | cut -d':' -f1)
        if [ -f "test-results/${service_name}.result" ]; then
            local result=$(cat "test-results/${service_name}.result")
            local status=$(echo $result | cut -d':' -f1)

            case $status in
                "SUCCESS")
                    echo "✅ $service_name - 启动成功"
                    ((success_count++))
                    ;;
                "FAIL")
                    echo "❌ $service_name - 启动失败"
                    ((fail_count++))
                    ;;
                "TIMEOUT")
                    echo "⏰ $service_name - 启动超时"
                    ((timeout_count++))
                    ;;
                "SKIP")
                    echo "⚠️  $service_name - 跳过测试"
                    ((skip_count++))
                    ;;
            esac
        else
            echo "❓ $service_name - 未测试"
        fi
    done

    echo ""
    echo "📊 统计结果："
    echo "   成功: $success_count"
    echo "   失败: $fail_count"
    echo "   超时: $timeout_count"
    echo "   跳过: $skip_count"
    echo ""
    echo "📁 详细日志请查看 test-results/ 目录"
}

# 主测试流程
echo ""
echo "开始测试服务启动..."

# 按优先级测试服务
priority_services=(
    "dhr-xxl-job-admin-service"
    "dhr-collection-service"
    "dhr-oauth-service"
    "dhr-gateway-service"
    "dhr-basic-service"
)

echo ""
echo "🎯 优先测试关键服务..."
for priority_service in "${priority_services[@]}"; do
    for service_info in "${SERVICES[@]}"; do
        local service_name=$(echo $service_info | cut -d':' -f1)
        if [ "$service_name" = "$priority_service" ]; then
            test_service_startup "$service_info"
            sleep 1
            break
        fi
    done
done

echo ""
echo "🔄 测试其他服务..."
for service_info in "${SERVICES[@]}"; do
    local service_name=$(echo $service_info | cut -d':' -f1)
    # 跳过已经测试过的优先服务
    local is_priority=false
    for priority_service in "${priority_services[@]}"; do
        if [ "$service_name" = "$priority_service" ]; then
            is_priority=true
            break
        fi
    done

    if [ "$is_priority" = false ]; then
        test_service_startup "$service_info"
        sleep 1
    fi
done

# 生成报告
generate_report

echo ""
echo "=== 测试完成 ==="
