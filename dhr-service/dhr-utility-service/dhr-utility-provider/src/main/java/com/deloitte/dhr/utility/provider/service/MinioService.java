package com.deloitte.dhr.utility.provider.service;

import java.io.InputStream;
public interface MinioService {

    String upload(String fileName, InputStream is, String contentType) throws Exception;

    String upload(String fileName, byte[] objectByte, String contentType) throws Exception;

    byte[] download(String fileName) throws Exception;

    void delete(String fileName) throws Exception;;

}
