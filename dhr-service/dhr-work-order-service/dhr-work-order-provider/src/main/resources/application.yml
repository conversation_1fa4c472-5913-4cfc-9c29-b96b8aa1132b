#nacos的相关配置
nacos:
  server-addr: ${config_nacos_serveraddr:localhost}
  port: ${config_nacos_port:8848}
  namespace: ${config_nacos_namespace:d55614cb-2190-4854-b324-c2b636ddc3e8}

spring:
  application:
    name: dhr-work-order-service
  profiles:
    active: ${config_profile:test}
  config:
    import:
      - optional:nacos:${spring.application.name}-${config_profile:test}.${spring.cloud.nacos.config.file-extension}
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.server-addr}:${nacos.port}
        namespace: ${nacos.namespace}
        group: ${spring.profiles.active}
      config:
        file-extension: yaml
        server-addr: ${nacos.server-addr}:${nacos.port}
        group: ${spring.profiles.active}
        enabled: ${config.enable:true}
        namespace: ${nacos.namespace}
        import-check:
          enabled: false
  messages:
    basename: i18n/messages
    encoding: utf-8
#端口号
server:
  port: ${config_server_port:9088}

# 日志配置
logging:
  config: classpath:log4j2_${spring.profiles.active}.xml

#logging:
#  file:
#    name: logs/${spring.application.name}/info/log_info.log
#  level:
#    root: info  #输出日志级别
#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
