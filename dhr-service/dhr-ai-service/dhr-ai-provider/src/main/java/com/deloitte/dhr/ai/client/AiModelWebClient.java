package com.deloitte.dhr.ai.client;

import com.alibaba.fastjson2.JSON;
import com.deloitte.dhr.ai.config.AiModelConfig;
import com.deloitte.dhr.ai.module.ai.pojo.AiModelRequest;
import com.deloitte.dhr.common.base.exception.CommRunException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;

@Slf4j
@Component
public class AiModelWebClient {

    @Autowired
    private AiModelConfig aiModelConfig;

    /**
     * 调用AI模型算法接口
     *
     * @param request 请求参数
     * @return Flux<String>
     */
    public Flux<String> post(AiModelRequest request) {
        AiModelConfig.Agent agent = aiModelConfig.getAgents().get(request.getType().getCode());
        if (agent == null) {
            return Flux.empty();
        }
        return WebClient.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(20 * 1024 * 1024))
                .baseUrl(aiModelConfig.getBaseUrl())
                .clientConnector(new ReactorClientHttpConnector(HttpClient.create().responseTimeout(Duration.ofMillis(50000))))
                .defaultHeader("Content-Type", "application/json")
                .build()
                .post()
                .uri(agent.getSuffixUrl())
                .bodyValue(JSON.toJSONString(request))
                .header("Authorization", "Bearer " + agent.getApiKey())
                .header("Content-Type", "application/json")
                .retrieve()
                .onStatus(HttpStatusCode::isError, response -> response.bodyToMono(String.class)
                        .flatMap(errorBody -> Mono.error(new CommRunException(errorBody))))
                .bodyToFlux(String.class);
    }

}
