package com.deloitte.dhr.ai.module.interview.resource.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.interview.category.mapper.AiInterviewCategoryMapper;
import com.deloitte.dhr.ai.module.interview.constant.AiInterviewConstant;
import com.deloitte.dhr.ai.module.interview.resource.domain.AiInterviewInfo;
import com.deloitte.dhr.ai.module.interview.resource.mapper.AiInterviewInfoMapper;
import com.deloitte.dhr.ai.module.interview.resource.pojo.*;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewInfoResponse;
import com.deloitte.dhr.ai.module.interview.resource.pojo.AiInterviewInfoSaveRequest;
import com.deloitte.dhr.ai.module.interview.resource.service.AiInterviewResourceAttachService;
import com.deloitte.dhr.ai.module.interview.resource.service.AiInterviewInfoService;
import com.deloitte.dhr.ai.module.interview.train.domain.AiInterviewDialogueQuestion;
import com.deloitte.dhr.ai.module.interview.train.pojo.*;
import com.deloitte.dhr.ai.module.interview.resource.service.AiInterviewDialogueQuestionService;
import com.deloitte.dhr.ai.module.interview.resource.service.AiInterviewObjectService;
import com.deloitte.dhr.ai.utils.CheckUtils;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.utils.StrUtils;
import com.deloitte.dhr.common.base.utils.date.DateStyle;
import com.deloitte.dhr.common.base.utils.date.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * AI访谈-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
public class AiInterviewInfoServiceImpl extends SuperServiceImpl<AiInterviewInfoMapper, AiInterviewInfo> implements AiInterviewInfoService {

    @Autowired
    private AiInterviewInfoMapper aiInterviewInfoMapper;
    @Autowired
    private AiInterviewResourceAttachService aiInterviewResourceAttachService;

    @Lazy
    @Autowired
    private AiInterviewInfoService aiInterviewInfoService;
    @Autowired
    private AiInterviewCategoryMapper aiInterviewCategoryMapper;
    @Autowired
    private AiInterviewObjectService aiInterviewObjectService;
    @Autowired
    private AiInterviewDialogueQuestionService aiInterviewDialogueQuestionService;


    @Override
    public AiInterviewInfoResponse getDetail(Long id) {
        AiInterviewInfo aiInterviewInfo = this.get(id);
        CheckUtils.checkNull(aiInterviewInfo, "数据不存在");
        return BeanUtil.copyProperties(aiInterviewInfo, AiInterviewInfoResponse.class);

    }

    @Override
    public ResponsePage<AiInterviewInfoResponse> findListPage(Page<AiInterviewInfo> page, AiInterviewListRequest request, BaseOrder order) {
        if (request.getCategoryId() != null) {
            request.setCategoryPath(aiInterviewCategoryMapper.getCategoryPathById(request.getCategoryId()));
        }
        List<AiInterviewInfoResponse> list = aiInterviewInfoMapper.findListPage(page, request, this.adjustOrder(order));
        return new ResponsePage<>(page, list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveData(AiInterviewInfoSaveRequest request, String courseType, String courseStatus) {
        // 保存/更新-课程资源-基本信息
        AiInterviewInfo aiInterviewInfo = BeanUtil.copyProperties(request, AiInterviewInfo.class);
        aiInterviewInfo.setCourseType(courseType);
        aiInterviewInfo.setCourseStatus(courseStatus);
        this.saveOrUpdate(aiInterviewInfo);
        return aiInterviewInfo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        // 删除草稿状态的数据
        AiInterviewInfo aiInterviewInfo = this.getById(id);
        CheckUtils.checkNull(aiInterviewInfo, "数据不存在");
        if (!StrUtil.equals(AiInterviewConstant.InterviewInfoStatus.DRAFT, aiInterviewInfo.getCourseStatus())) {
            throw new CommRunException("非草稿状态数据无法删除");
        }
        // 删除AI访谈-基本信息数据
        aiInterviewInfoMapper.deleteById(id);

        // 删除AI访谈-学习资料附件数据
        aiInterviewResourceAttachService.deleteByResourceId(id);

        return true;
    }

    public void verify(AiInterviewSaveRequest request) {
        LambdaQueryWrapper<AiInterviewInfo> queryWrapper = new LambdaQueryWrapper<>();

        AiInterviewInfoSaveRequest resourceInfo = request.getResourceInfo();
        // 校验-是否允许修改
        if (resourceInfo.getId() != null) {
            queryWrapper.eq(AiInterviewInfo::getId, resourceInfo.getId());
            queryWrapper.eq(AiInterviewInfo::getCourseStatus, AiInterviewConstant.InterviewInfoStatus.SUBMIT);
            Long existSubmit = aiInterviewInfoMapper.selectCount(queryWrapper);
            if (existSubmit > 0) {
                throw new CommRunException("无法修改已提交数据");
            }
        }

        // 校验-课程资源名称
        queryWrapper.clear();
        queryWrapper.eq(AiInterviewInfo::getCourseName, resourceInfo.getCourseName());
        queryWrapper.ne(resourceInfo.getId() != null, AiInterviewInfo::getId, resourceInfo.getId());
        Long existCourseName = aiInterviewInfoMapper.selectCount(queryWrapper);
        if (existCourseName > 0) {
            throw new CommRunException("课程名称已存在");
        }

        // 校验-沟通框架
        // aiInterviewCommunicateFrameworkService.verify(request.getFrameworkInfoList());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveAll(AiInterviewSaveRequest request, String courseStatus) {

        // 保存前校验
        this.verify(request);

        // 保存/更新-AI访谈-基本信息
        Long resourceId = aiInterviewInfoService.saveData(request.getResourceInfo(), AiInterviewConstant.InterviewInfoType.AIFT, courseStatus);

        // 保存/更新-AI访谈-学习附件信息
        aiInterviewResourceAttachService.saveData(request.getAttachInfoList(), resourceId);

        // 保存/更新-AI访谈对象信息
        if("02".equals(request.getObjectInfo().getObjectAvatarType())
            && StrUtils.isEmpty(request.getObjectInfo().getObjectAvatarUrl())){
            throw new CommRunException("请上传角色头像");
        }
        aiInterviewObjectService.saveData(request.getObjectInfo(), resourceId);


        // 保存/更新-AI陪练-对话提示信息
        List<AiInterviewDialogueQuestion> questionList = aiInterviewDialogueQuestionService.saveData(request.getQuestionInfoList(), resourceId);

        // 保存-AI问题语音信息
        // 取消AI问题的文字转语言-20250620
        /*if (StrUtil.equals(courseStatus, AiInterviewConstant.InterviewInfoStatus.SUBMIT)) {
            aiInterviewDialogueQuestionService.saveAiQuestionVoice(questionList);
        }*/
        return resourceId;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitAll(AiInterviewSaveRequest request) {

        // 保存数据
        return this.saveAll(request, AiInterviewConstant.InterviewInfoStatus.SUBMIT);
    }

    @Override
    public AiInterviewDetailResponse getInterviewDetail(Long resourceId) {

        // 查询-课程资源-基本信息详情数据
        AiInterviewInfoResponse resourceInfo = aiInterviewInfoService.getDetail(resourceId);
        if (!StrUtil.equals(resourceInfo.getCourseType(), AiInterviewConstant.InterviewInfoType.AIFT)) {
            return null;
        }
        AiInterviewDetailResponse response = new AiInterviewDetailResponse();
        response.setResourceInfo(resourceInfo);

        // 查询-课程资源-学习附件信息详情数据
        response.setAttachInfoList(aiInterviewResourceAttachService.getByResourceId(resourceId));

        // 查询-AI访谈对象信息
        response.setObjectInfo(aiInterviewObjectService.getByResourceId(resourceId));

        // 查询-AI陪练-沟通框架信息
        List<AiInterviewDialogueQuestionResponse> questionList = aiInterviewDialogueQuestionService.getByResourceId(resourceId);
        List<AiInterviewTestQuestionResponse> questionResponseList = new ArrayList<>();
        questionList.forEach(question -> {
            AiInterviewTestQuestionResponse questionResponse = BeanUtil.copyProperties(question, AiInterviewTestQuestionResponse.class);
            questionResponse.setQuestionType(AiInterviewConstant.InterviewQuestionType.ESSAY_QUESTION);
            questionResponseList.add(questionResponse);
        });
        response.setQuestionInfoList(questionResponseList);

        return response;
    }

    @Override
    public Long copyInterview(Long resourceId) {
        // 查询-课程资源-基本信息详情数据
        AiInterviewInfoResponse resourceInfo = aiInterviewInfoService.getDetail(resourceId);
        if (Objects.isNull(resourceInfo)) {
            throw new CommRunException("查询数据为空");
        }
        resourceInfo.setCourseName(resourceInfo.getCourseName() + "-"+ DateUtils.format(new Date(), DateStyle.YYYYMMDDHHMMSS.getValue()));
        resourceInfo.setId(null);
        Long newResourceId = aiInterviewInfoService.saveData(BeanUtil.copyProperties(resourceInfo, AiInterviewInfoSaveRequest.class), AiInterviewConstant.InterviewInfoType.AIFT, AiInterviewConstant.InterviewInfoStatus.DRAFT);

        // 查询-课程资源-学习附件信息详情数据
        List<AiInterviewResourceAttachResponse> attachInfoList =aiInterviewResourceAttachService.getByResourceId(resourceId);
        aiInterviewResourceAttachService.saveData(BeanUtil.copyToList(attachInfoList, AiInterviewResourceAttachSaveRequest.class), newResourceId);

        // 查询-AI访谈对象信息
        AiInterviewObjectResponse objectResponse = aiInterviewObjectService.getByResourceId(resourceId);
        aiInterviewObjectService.saveData(BeanUtil.copyProperties(objectResponse, AiInterviewObjectSaveRequest.class), newResourceId);

        // 查询-AI陪练-对话提示信息
        List<AiInterviewDialogueQuestionResponse> questionList = aiInterviewDialogueQuestionService.getByResourceId(resourceId);
        List<AiInterviewDialogueQuestion> questionList2 = aiInterviewDialogueQuestionService.saveData(BeanUtil.copyToList(questionList,  AiInterviewTestQuestionSaveRequest.class), newResourceId);

        return newResourceId;
    }
}

