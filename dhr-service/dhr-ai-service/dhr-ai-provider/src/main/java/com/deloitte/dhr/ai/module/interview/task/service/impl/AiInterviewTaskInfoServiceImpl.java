package com.deloitte.dhr.ai.module.interview.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.ai.module.interview.category.service.AiInterviewCategoryService;
import com.deloitte.dhr.ai.module.interview.constant.AiInterviewConstant;
import com.deloitte.dhr.ai.module.interview.task.domain.AiInterviewTaskEmp;
import com.deloitte.dhr.ai.module.interview.task.domain.AiInterviewTaskInfo;
import com.deloitte.dhr.ai.module.interview.task.mapper.AiInterviewTaskInfoMapper;
import com.deloitte.dhr.ai.module.interview.task.pojo.*;
import com.deloitte.dhr.ai.module.interview.task.service.AiInterviewTaskEmpService;
import com.deloitte.dhr.ai.module.interview.task.service.AiInterviewTaskInfoService;
import com.deloitte.dhr.ai.utils.CheckUtils;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.exception.CommRunException;
import com.deloitte.dhr.common.base.pojo.UserDto;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 * AI访谈-访谈任务-相关业务接口实现
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
public class AiInterviewTaskInfoServiceImpl extends SuperServiceImpl<AiInterviewTaskInfoMapper, AiInterviewTaskInfo> implements AiInterviewTaskInfoService {

    @Autowired
    private AiInterviewTaskInfoMapper aiInterviewTaskInfoMapper;
    @Autowired
    private AiInterviewTaskEmpService aiInterviewTaskEmpService;
    @Autowired
    private AiInterviewCategoryService aiInterviewCategoryService;

    @Override
    public AiInterviewTaskDetailResponse getInterviewTaskDetail(Long id) {
        AiInterviewTaskDetailResponse interviewTaskDetail = aiInterviewTaskInfoMapper.getInterviewTaskDetail(id);
        interviewTaskDetail.setWksEmpNum(interviewTaskDetail.getWksEmpNum() == null ? Integer.valueOf(0) : interviewTaskDetail.getWksEmpNum());
        interviewTaskDetail.setJxzEmpNum(interviewTaskDetail.getJxzEmpNum() == null ? Integer.valueOf(0) : interviewTaskDetail.getJxzEmpNum());
        interviewTaskDetail.setYwcEmpNum(interviewTaskDetail.getYwcEmpNum() == null ? Integer.valueOf(0) : interviewTaskDetail.getYwcEmpNum());

        interviewTaskDetail.setCategoryName(aiInterviewCategoryService.getCategoryPathNameById(interviewTaskDetail.getCategoryId()));
        return interviewTaskDetail;
    }

    @Override
    public ResponsePage<AiInterviewTaskResponse> findTaskListPage(Page<AiInterviewTaskInfo> page, AiInterviewTaskListRequest request, BaseOrder order) {
        List<AiInterviewTaskResponse> list = aiInterviewTaskInfoMapper.findListPage(page, request, this.adjustOrder(order));
        list.forEach(response -> {
            response.setWksEmpNum(response.getWksEmpNum() == null ? Integer.valueOf(0) : response.getWksEmpNum());
            response.setJxzEmpNum(response.getJxzEmpNum() == null ? Integer.valueOf(0) : response.getJxzEmpNum());
            response.setYwcEmpNum(response.getYwcEmpNum() == null ? Integer.valueOf(0) : response.getYwcEmpNum());
        });
        return new ResponsePage<>(page, list);
    }

    @Override
    public ResponsePage<AiMyInterviewTaskListResponse> myTaskList(Page<AiInterviewTaskInfo> page, AiInterviewEmpTaskListRequest request, BaseOrder order) {
        UserDto loginUser = LoginUtil.getLoginUser();
        if (loginUser == null || StrUtil.isBlank(loginUser.getUsername())) {
            return new ResponsePage<>(page, Collections.emptyList());
        }
        request.setLoginEmpCode(loginUser.getUsername());

        // 如果查询的是已结束的访谈任务
        if (StrUtil.equals(request.getTaskStatus(), AiInterviewConstant.TaskStatus.YJS)) {
            List<AiMyInterviewTaskListResponse> list = aiInterviewTaskInfoMapper.myTaskList2(page, request, this.adjustOrder(order));
            return new ResponsePage<>(page, list);
        } else {
            List<AiMyInterviewTaskListResponse> list = aiInterviewTaskInfoMapper.myTaskList(page, request, this.adjustOrder(order));
            return new ResponsePage<>(page, list);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveData(AiInterviewTaskSaveRequest request, String taskStatus) {

        // 校验-访谈任务保存信息
        this.verify(request);

        // 保存/更新-访谈任务-基本信息
        AiInterviewTaskInfo taskInfo = BeanUtil.copyProperties(request.getTaskInfo(), AiInterviewTaskInfo.class);
        taskInfo.setTaskStatus(taskStatus);
        this.saveOrUpdate(taskInfo);

        // 保存/更新-访谈任务-员工基本信息
        aiInterviewTaskEmpService.saveData(request.getTaskEmpInfoList(), taskInfo.getId(), taskInfo.getResourceId());

        return taskInfo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long publishData(AiInterviewTaskSaveRequest request) {
        return this.saveData(request, AiInterviewConstant.TaskStatus.JXZ);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteById(Long id) {
        AiInterviewTaskInfo taskInfo = aiInterviewTaskInfoMapper.selectById(id);
        CheckUtils.checkNull(taskInfo, "该数据不存在");
        if (!StrUtil.equals(taskInfo.getTaskStatus(), AiInterviewConstant.TaskStatus.DRAFT)) {
            throw new CommRunException("已提交数据不能删除");
        }

        // 删除-访谈任务-基本信息
        this.removeById(id);

        // 删除-访谈任务-员工信息
        aiInterviewTaskEmpService.deleteByTaskId(id);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean closeTask(Long id) {

        // 修改任务状态为已结束
        AiInterviewTaskInfo taskInfo = aiInterviewTaskInfoMapper.selectById(id);
        taskInfo.setTaskStatus(AiInterviewConstant.TaskStatus.YJS);
        aiInterviewTaskInfoMapper.updateById(taskInfo);
        return true;
    }

    /**
     * 校验-访谈任务
     *
     * @param request 访谈任务保存信息
     */
    public void verify(AiInterviewTaskSaveRequest request) {

        // 校验-是否允许修改
        this.verifyTaskInfo(request.getTaskInfo());

        // 校验-访谈任务名称
        this.verifyTaskName(request.getTaskInfo());

        // 校验-访谈任务员工
        aiInterviewTaskEmpService.verifyTaskEmp(request.getTaskEmpInfoList());

    }

    /**
     * 校验-访谈任务是否允许修改
     *
     * @param saveRequest 访谈任务基本保存信息
     */
    private void verifyTaskInfo(AiInterviewTaskInfoSaveRequest saveRequest) {
        // 校验-是否允许修改
        if (saveRequest.getId() == null) {
            return;
        }
        LambdaQueryWrapper<AiInterviewTaskInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiInterviewTaskInfo::getId, saveRequest.getId());
        queryWrapper.ne(AiInterviewTaskInfo::getTaskStatus, AiInterviewConstant.TaskStatus.DRAFT);
        Long existSubmit = aiInterviewTaskInfoMapper.selectCount(queryWrapper);
        if (existSubmit > 0) {
            throw new CommRunException("无法修改已提交数据");
        }
    }

    /**
     * 校验-访谈任务名称是否重复
     *
     * @param saveRequest 访谈任务基本保存信息
     */
    private void verifyTaskName(AiInterviewTaskInfoSaveRequest saveRequest) {
        // 校验-访谈任务名称
        LambdaQueryWrapper<AiInterviewTaskInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiInterviewTaskInfo::getTaskName, saveRequest.getTaskName());
        queryWrapper.ne(saveRequest.getId() != null, AiInterviewTaskInfo::getId, saveRequest.getId());
        Long existTaskName = aiInterviewTaskInfoMapper.selectCount(queryWrapper);
        if (existTaskName > 0) {
            throw new CommRunException("访谈任务名称已存在");
        }
    }

}

