nacos:
  server-addr: ${config_nacos_serveraddr:localhost}
  port: ${config_nacos_port:8848}
  namespace: ${config_nacos_namespace:d55614cb-2190-4854-b324-c2b636ddc3e8}


spring:
  application:
    name: dhr-oauth-service
  profiles:
    active: ${config_profile:test}
  config:
    import:
      - optional:nacos:${spring.application.name}-${config_profile:test}.${spring.cloud.nacos.config.file-extension}
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  mvc: # swagger启动报错配置
    pathmatch:
      matching-strategy: ant_path_matcher
  thymeleaf:
    cache: false
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.server-addr}:${nacos.port}
        namespace: ${nacos.namespace}
        group: ${spring.profiles.active}
      config:
        file-extension: yaml
        server-addr: ${nacos.server-addr}:${nacos.port}
        group: ${spring.profiles.active}
        enabled: ${config.enable:true}
        namespace: ${nacos.namespace}
        import-check:
          enabled: false
  #i18n
  messages:
    basename: i18n.message
    encoding: UTF-8
server:
  port: ${config_server_port:9006}
#  servlet:
#    context-path: ${config_context_path:/dhr/dev/oa}


# encrypt:
#   key-store:
#     location: classpath:mytest.jks
#     secret: mypass
#     alias: mytest

custom:
  remote:
    user-url: http://localhost:8420/bs/datacenter/dc-employee/list/queryNoAuthEmps

logout:
  url: https://psc.deloitte.com.cn/dhr/demo/web/
  app-url: https://psc.deloitte.com.cn/dhr/demo/app

logging:
  file:
    name: logs/${spring.application.name}/info/log_info.log
  level:
    root: info  #输出日志级别

dhr:
  swagger:
    enabled: true
    docket:
      basic:
        title: 授权中心
        base-package: com

