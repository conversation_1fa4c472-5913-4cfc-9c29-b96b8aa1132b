<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dhr-questionnaire-service</artifactId>
        <groupId>com.deloitte</groupId>
        <version>3.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>dhr-questionnaire-provider</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.deloitte</groupId>
            <artifactId>dhr-questionnaire-common-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
            <version>3.1.5</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper-base</artifactId>
            <version>${mapper-base.version}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <!-- 添加依赖管理来解决传递依赖的安全漏洞 -->
    <dependencyManagement>
        <dependencies>
            <!-- 解决 Spring 安全漏洞 -->
            <dependency>
                <groupId>org.springframework</groupId>
