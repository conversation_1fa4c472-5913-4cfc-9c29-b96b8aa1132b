nacos:
  server-addr: ${config_nacos_serveraddr:localhost}
  port: ${config_nacos_port:8848}
  namespace: ${config_nacos_namespace:d55614cb-2190-4854-b324-c2b636ddc3e8}

spring:
  application:
    name: dhr-perf-service
  profiles:
    active: ${config_profile:test}
  config:
    import:
      - optional:nacos:${spring.application.name}-${config_profile:test}.${spring.cloud.nacos.config.file-extension}
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.server-addr}:${nacos.port}
        namespace: ${nacos.namespace}
        group: ${spring.profiles.active}
      config:
        file-extension: yaml
        server-addr: ${nacos.server-addr}:${nacos.port}
        group: ${spring.profiles.active}
        enabled: ${config.enable:true}
        namespace: ${nacos.namespace}
        import-check:
          enabled: false
  #i18n
  messages:
    basename: i18n.message
    encoding: UTF-8

server:
  port: ${config_server_port:9028}

dhr:
  swagger:
    enabled: true
    docket:
      basic:
        title: 绩效模块
        base-package: com.deloitte.dhr.performance #需要扫描的包名
  task:
    core-pool-size: 10 #核心线程
    queue-capacity: 5  #任务队列长度
    max-pool-size: 20  #最大线程树
    thread-name-prefix: dhr_basic_performance  #任务线程前置名

logging:
  file:
    name: logs/${spring.application.name}/info/log_info.log
  level:
    root: info  #输出日志级别


