package com.deloitte.dhr.ssc.module.portal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deloitte.dhr.common.BaseOrder;
import com.deloitte.dhr.common.ResponsePage;
import com.deloitte.dhr.common.SuperServiceImpl;
import com.deloitte.dhr.common.base.utils.LoginUtil;
import com.deloitte.dhr.common.base.utils.MessageUtils;
import com.deloitte.dhr.excel.parser.handler.I18nCellWriteHandler;
import com.deloitte.dhr.excel.parser.pojo.entity.ExcelSheet;
import com.deloitte.dhr.excel.parser.pojo.entity.ExcelSheetData;
import com.deloitte.dhr.excel.parser.template.ExcelParserTemplate;
import com.deloitte.dhr.excel.parser.util.MessageUtil;
import com.deloitte.dhr.mda.module.base.pojo.EmployeeBasicInfoResp;
import com.deloitte.dhr.mda.module.base.pojo.EmployeePhotoResp;
import com.deloitte.dhr.mda.module.base.util.DictUtil;
import com.deloitte.dhr.ssc.module.message.pojo.DhrMiCommonMessageRequest;
import com.deloitte.dhr.ssc.module.message.service.DhrMiCommonMessageService;
import com.deloitte.dhr.ssc.module.portal.constant.DhrPtConstant;
import com.deloitte.dhr.ssc.module.portal.domain.DhrNiCommentInfo;
import com.deloitte.dhr.ssc.module.portal.domain.DhrNiCommentLikeInfo;
import com.deloitte.dhr.ssc.module.portal.domain.NiOrgRel;
import com.deloitte.dhr.ssc.module.portal.excel.dto.DhrNiCommentInfoDTO;
import com.deloitte.dhr.ssc.module.portal.mapper.DhrNiCommentInfoMapper;
import com.deloitte.dhr.ssc.module.portal.mapper.DhrNiCommentLikeInfoMapper;
import com.deloitte.dhr.ssc.module.portal.mapper.NiOrgRelMapper;
import com.deloitte.dhr.ssc.module.portal.pojo.DhrNiCommentCountResponse;
import com.deloitte.dhr.ssc.module.portal.pojo.DhrNiCommentInfoAddRequest;
import com.deloitte.dhr.ssc.module.portal.pojo.DhrNiCommentInfoBannerRequest;
import com.deloitte.dhr.ssc.module.portal.pojo.DhrNiCommentInfoBannerResponse;
import com.deloitte.dhr.ssc.module.portal.pojo.DhrNiCommentInfoConcentrateRequest;
import com.deloitte.dhr.ssc.module.portal.pojo.DhrNiCommentInfoPushResponse;
import com.deloitte.dhr.ssc.module.portal.pojo.DhrNiCommentInfoRequest;
import com.deloitte.dhr.ssc.module.portal.pojo.DhrNiCommentInfoResponse;
import com.deloitte.dhr.ssc.module.portal.pojo.DhrNiCommentInfoStatusAllRequest;
import com.deloitte.dhr.ssc.module.portal.pojo.DhrNiCommentInfoStatusRequest;
import com.deloitte.dhr.ssc.module.portal.service.DhrNiCommentInfoService;
import com.deloitte.dhr.ssc.util.SscCommonUtil;
import com.deloitte.dhr.ssc.util.SscFeignMethodUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * dhr_ni_comment_info首页-公告管理-公告评论表;
 *
 * <AUTHOR>
 * @date 2022-12-14
 */
@Service
public class DhrNiCommentInfoServiceImpl extends SuperServiceImpl<DhrNiCommentInfoMapper, DhrNiCommentInfo> implements DhrNiCommentInfoService {

    @Resource
    private DhrNiCommentInfoMapper dhrNiCommentInfoMapper;

    @Autowired
    private SscFeignMethodUtil sscFeignMethodUtil;

    @Autowired
    private ExcelParserTemplate excelParserTemplate;

    @Resource
    private NiOrgRelMapper niOrgRelMapper;

    @Resource
    private DhrNiCommentLikeInfoMapper niCommentLikeInfoMapper;

    @Autowired
    private DhrMiCommonMessageService miCommonMessageService;


    @Override
    public List<DhrNiCommentInfoResponse> getReplyCommentList(Long id) {
        LambdaQueryWrapper<DhrNiCommentInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DhrNiCommentInfo::getReplyCommentId, id);
        List<DhrNiCommentInfo> dhrNiCommentInfos = dhrNiCommentInfoMapper.selectList(wrapper);
        List<DhrNiCommentInfoResponse> list = BeanUtil.copyToList(dhrNiCommentInfos, DhrNiCommentInfoResponse.class);
        // 转义
        if (CollUtil.isNotEmpty(dhrNiCommentInfos)) {
            List<String> empCodeList = list.stream().map(DhrNiCommentInfoResponse::getCreateBy).collect(Collectors.toList());
            Map<String, EmployeeBasicInfoResp> byEmpCodes = sscFeignMethodUtil.findByEmpCodes(empCodeList);
            list.forEach(item -> {
                // 创建人名称
                EmployeeBasicInfoResp employeeBasicInfoResp = byEmpCodes.get(item.getCreateBy());
                if (employeeBasicInfoResp != null) {
                    item.setCreateName(employeeBasicInfoResp.getFullname());
                }
            });
        }
        DictUtil.converValue(list);
        return list;
    }

    @Override
    public ResponsePage<DhrNiCommentInfoResponse> findListPage(Page<DhrNiCommentInfo> page,
                                                               DhrNiCommentInfoRequest request,
                                                               BaseOrder order) {
        String commentType = request.getCommentType();
        if (StrUtil.isBlank(commentType)) {
            request.setCommentType(DhrPtConstant.CommentType.YS);
        }
        List<DhrNiCommentInfoResponse> list = dhrNiCommentInfoMapper.findListPage(page, request, this.adjustOrder(order));
        // 转义
        if (CollUtil.isNotEmpty(list)) {
            List<String> empCodeList = list.stream().map(DhrNiCommentInfoResponse::getCreateBy).collect(Collectors.toList());
            Map<String, EmployeeBasicInfoResp> byEmpCodes = sscFeignMethodUtil.findByEmpCodes(empCodeList);
            list.forEach(item -> {
                // 创建人名称
                EmployeeBasicInfoResp employeeBasicInfoResp = byEmpCodes.get(item.getCreateBy());
                if (employeeBasicInfoResp != null) {
                    item.setCreateName(employeeBasicInfoResp.getFullname());
                }
                // 回复评论数量
                Integer replyCommentTotal = countReplyCommentTotal(item.getId());
                item.setReplyCommentTotal(replyCommentTotal);
            });
        }
        return new ResponsePage<>(page, list);
    }

    @Override
    public ResponsePage<DhrNiCommentInfoBannerResponse> findBannerListPage(Page<DhrNiCommentInfo> page, DhrNiCommentInfoBannerRequest request, BaseOrder order) {
        String username = LoginUtil.getLoginUser().getUsername();
        // String username = "SYSTEM";
        List<DhrNiCommentInfoBannerResponse> list = dhrNiCommentInfoMapper.findBannerListPage(page, request, this.adjustOrder(order), username);
        if (CollUtil.isNotEmpty(list)) {
            List<Long> replyCommentIdList = list.stream().map(DhrNiCommentInfoBannerResponse::getId).collect(Collectors.toList());
            List<String> empCodeList = list.stream().map(DhrNiCommentInfoBannerResponse::getCreateBy).collect(Collectors.toList());
            List<DhrNiCommentInfoBannerResponse> replyList = dhrNiCommentInfoMapper.findBannerReplyList(request, this.adjustOrder(order), username, replyCommentIdList);
            // 填充回复评论
            Map<Long, List<DhrNiCommentInfoBannerResponse>> replyMap = new HashMap<>(16);
            if (CollUtil.isNotEmpty(replyList)) {
                replyMap = replyList.stream().collect(Collectors.groupingBy(DhrNiCommentInfoBannerResponse::getReplyCommentId));
                empCodeList.addAll(replyList.stream().map(DhrNiCommentInfoBannerResponse::getCreateBy).collect(Collectors.toList()));
            }
            // 填充创建人名称、点赞统计
            Map<String, EmployeeBasicInfoResp> byEmpCodes = sscFeignMethodUtil.findByEmpCodes(empCodeList.stream().distinct().collect(Collectors.toList()));
            // 转义
            escapeCommentInfoBanner(list, byEmpCodes, username);
            escapeCommentInfoBanner(replyList, byEmpCodes, username);
            for (DhrNiCommentInfoBannerResponse item : list) {
                item.setReplyCommentList(replyMap.get(item.getId()));
            }
        }
        return new ResponsePage<>(page, list);
    }

    /**
     * 转义相关信息
     *
     * @param list
     * @param byEmpCodes 用户姓名MAP
     * @param username   当前用户
     */
    private void escapeCommentInfoBanner(List<DhrNiCommentInfoBannerResponse> list, Map<String, EmployeeBasicInfoResp> byEmpCodes, String username) {
        list.forEach(item -> {
            EmployeeBasicInfoResp employeeBasicInfoResp = byEmpCodes.get(item.getCreateBy());
            if (employeeBasicInfoResp != null) {
                item.setCreateName(employeeBasicInfoResp.getFullname());
                EmployeePhotoResp photoResp = sscFeignMethodUtil.findByEmpCodes(item.getCreateBy());
                item.setCreatePhoto(photoResp==null?null:photoResp.getEmpPhoto());
            }
            Integer likeTotal = countCommentLikeTotal(item.getId());
            item.setLikeCount(likeTotal);
            if (item.getCreateBy() != null && item.getCreateBy().equals(username)) {
                item.setOwner(true);
            }
        });
    }

    /**
     * 统计评论的点赞数量
     *
     * @param id
     * @return
     */
    private Integer countCommentLikeTotal(Long id) {
        LambdaQueryWrapper<DhrNiCommentLikeInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(DhrNiCommentLikeInfo::getId);
        wrapper.eq(DhrNiCommentLikeInfo::getCommentId, id);
        return niCommentLikeInfoMapper.selectCount(wrapper);
    }

    /**
     * 获取回复评论数量
     *
     * @param commentId
     * @return
     */
    private Integer countReplyCommentTotal(Long commentId) {
        LambdaQueryWrapper<DhrNiCommentInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(DhrNiCommentInfo::getId);
        wrapper.eq(DhrNiCommentInfo::getReplyCommentId, commentId);
        return this.getBaseMapper().selectCount(wrapper);
    }

    @Override
    public Boolean saveData(DhrNiCommentInfoAddRequest request) {
        DhrNiCommentInfo dhrNiCommentInfo = new DhrNiCommentInfo();
        BeanUtils.copyProperties(request, dhrNiCommentInfo);
        // 设置评论状态为：待审核
        dhrNiCommentInfo.setCommentStatus(DhrPtConstant.CommentStatus.DSH);
        dhrNiCommentInfo.setHasConcentrate(false);
        return this.insert(dhrNiCommentInfo);
    }


    @Override
    public Boolean batchDelete(List<Long> ids) {
        return this.delete(ids);
    }

    @Override
    public Boolean delete(Long id) {
        int rows = this.getBaseMapper().deleteById(id);
        return rows > 0;
    }

    @Override
    public DhrNiCommentCountResponse countNiComment(String noticeCode) {
        DhrNiCommentCountResponse commentCountResponse = new DhrNiCommentCountResponse();
        commentCountResponse.setNoticeCode(noticeCode);
        // 查询所有评论
        LambdaQueryWrapper<DhrNiCommentInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DhrNiCommentInfo::getNoticeCode, noticeCode);
        Long allCommentTotal = this.getBaseMapper().selectCount(queryWrapper);
        commentCountResponse.setAllCommentTotal(allCommentTotal);
        queryWrapper.clear();
        queryWrapper.eq(DhrNiCommentInfo::getNoticeCode, noticeCode);
        queryWrapper.eq(DhrNiCommentInfo::getHasConcentrate, true);
        Long concentrateTotal = this.getBaseMapper().selectCount(queryWrapper);
        commentCountResponse.setConcentrateTotal(concentrateTotal);
        return commentCountResponse;
    }

    @Override
    public Boolean updateStatus(DhrNiCommentInfoStatusRequest request) {
        List<Long> ids = request.getIds();
        String commentStatus = request.getCommentStatus();
        if (CollUtil.isNotEmpty(ids) && StrUtil.isNotBlank(commentStatus)) {
            LambdaUpdateWrapper<DhrNiCommentInfo> wrapper = new LambdaUpdateWrapper();
            wrapper.set(DhrNiCommentInfo::getCommentStatus, commentStatus);
            // 取消通过同时取消精选状态
            if (DhrPtConstant.CommentStatus.DSH.equals(commentStatus)) {
                wrapper.set(DhrNiCommentInfo::getHasConcentrate, false);
            }
            // 审批通过设置审批人信息
            setApproveInfo(wrapper, commentStatus);
            wrapper.in(DhrNiCommentInfo::getId, ids);
            int rows = this.getBaseMapper().update(null, wrapper);
            // 推送消息
            pushStatusMessage(request);
            return rows > 0;
        }
        return false;
    }

    /**
     * 更新评论状态，推送消息通知
     *
     * @param request
     * @return
     */
    private Boolean pushStatusMessage(DhrNiCommentInfoStatusRequest request) {
        if (DhrPtConstant.CommentStatus.SHTG.equals(request.getCommentStatus())) {
            // 获取评论人列表
            List<DhrNiCommentInfoPushResponse> dhrNiCommentInfos = dhrNiCommentInfoMapper.findPushListByIds(request.getIds());
            List<DhrMiCommonMessageRequest> messageRequests = new ArrayList<>();
            for (DhrNiCommentInfoPushResponse pushResponse : dhrNiCommentInfos) {
                // 组装评论通过审核相关消息
                String title = pushResponse.getTitle();
                String commentContent = pushResponse.getCommentContent();
                DhrMiCommonMessageRequest messageRequest = new DhrMiCommonMessageRequest();
                String language = pushResponse.getLanguage();
                String message = MessageUtils.toLocaleSpecifyLanguage("dhr.ssc.comment.message.Approved", language, title, commentContent);
                messageRequest.setTitle(message);
                messageRequest.setMessage(message);
                messageRequest.setMessageType(DhrPtConstant.CommentMessage.TYPE);
                messageRequest.setSiteFilter(DhrPtConstant.CommentMessage.SITE_FILTER_IN);
                messageRequest.setMessageDate(new Date());
                messageRequest.setEmpCode(pushResponse.getCreateBy());
                messageRequest.setViewStatus(DhrPtConstant.CommentMessage.NOT_READ);
                messageRequests.add(messageRequest);

            }
            Boolean result = miCommonMessageService.insertOrUpdateBatch(messageRequests);
            // 推送回复类消息
            List<DhrNiCommentInfoPushResponse> replyNiCommentInfos = dhrNiCommentInfos.stream().filter(r -> DhrPtConstant.CommentType.HF.equals(r.getCommentType())).collect(Collectors.toList());
            List<String> createByList = replyNiCommentInfos.stream().map(DhrNiCommentInfoPushResponse::getCreateBy).collect(Collectors.toList());
            Map<String, EmployeeBasicInfoResp> empCodes = sscFeignMethodUtil.findByEmpCodes(createByList);
            List<DhrMiCommonMessageRequest> replyMessageRequests = new ArrayList<>();
            for (DhrNiCommentInfoPushResponse replyResponse : dhrNiCommentInfos) {
                String language = replyResponse.getLanguage();
                String title = replyResponse.getTitle();
                String commentContent = replyResponse.getCommentContent();
                String replyCommentContent = replyResponse.getReplyCommentContent();
                String replyCreateBy = replyResponse.getReplyCreateBy();
                String createBy = replyResponse.getCreateBy();
                EmployeeBasicInfoResp employeeBasicInfoResp = empCodes.get(createBy);
                String replyCreateByName = "";
                if (employeeBasicInfoResp != null) {
                    replyCreateByName = employeeBasicInfoResp.getFullname();
                }
                // 组装回复评论消息
                DhrMiCommonMessageRequest replyMessageRequest = new DhrMiCommonMessageRequest();
                String replyTitle = MessageUtils.toLocaleSpecifyLanguage("dhr.ssc.comment.message.replyTitle", language, replyCreateByName);
                String replyMessage = MessageUtils.toLocaleSpecifyLanguage("dhr.ssc.comment.message.reply", language, replyCreateByName, title, replyCommentContent, commentContent);
                replyMessageRequest.setTitle(replyTitle);
                replyMessageRequest.setMessage(replyMessage);
                replyMessageRequest.setMessageType(DhrPtConstant.CommentMessage.TYPE);
                replyMessageRequest.setSiteFilter(DhrPtConstant.CommentMessage.SITE_FILTER_IN);
                replyMessageRequest.setMessageDate(new Date());
                replyMessageRequest.setEmpCode(replyCreateBy);
                replyMessageRequest.setViewStatus(DhrPtConstant.CommentMessage.NOT_READ);
                replyMessageRequests.add(replyMessageRequest);
            }
            if (CollUtil.isNotEmpty(replyMessageRequests)) {
                miCommonMessageService.insertOrUpdateBatch(replyMessageRequests);
            }
            return result;
        }
        return false;
    }


    @Override
    public Boolean updateAllStatus(DhrNiCommentInfoStatusAllRequest request) {
        String noticeCode = request.getNoticeCode();
        String commentStatus = request.getCommentStatus();
        if (StrUtil.isNotBlank(noticeCode) && StrUtil.isNotBlank(commentStatus)) {
            LambdaUpdateWrapper<DhrNiCommentInfo> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(DhrNiCommentInfo::getCommentStatus, commentStatus);
            // 审批通过设置审批人信息
            setApproveInfo(wrapper, commentStatus);
            wrapper.eq(DhrNiCommentInfo::getNoticeCode, noticeCode);
            wrapper.eq(DhrNiCommentInfo::getCommentStatus, DhrPtConstant.CommentStatus.DSH);
            int rows = this.getBaseMapper().update(null, wrapper);
            return rows > 0;
        }
        return true;
    }

    /**
     * 设置审批信息
     *
     * @param wrapper
     * @param commentStatus
     */
    private void setApproveInfo(LambdaUpdateWrapper<DhrNiCommentInfo> wrapper, String commentStatus) {
        // 审批通过设置审批人信息
        if (DhrPtConstant.CommentStatus.SHTG.equals(commentStatus)) {
            wrapper.set(DhrNiCommentInfo::getApprover, SscCommonUtil.fillLoginEmpCode());
            wrapper.set(DhrNiCommentInfo::getApproverName, SscCommonUtil.fillLoginEmpName());
            wrapper.set(DhrNiCommentInfo::getApproverTime, new Date());
        } else {
            wrapper.set(DhrNiCommentInfo::getApprover, null);
            wrapper.set(DhrNiCommentInfo::getApproverName, null);
            wrapper.set(DhrNiCommentInfo::getApproverTime, null);
        }
    }

    @Override
    public Boolean updateConcentrate(DhrNiCommentInfoConcentrateRequest request) {
        List<Long> ids = request.getIds();
        Boolean concentrate = request.getHasConcentrate();
        if (CollUtil.isNotEmpty(ids) && concentrate != null) {
            LambdaUpdateWrapper<DhrNiCommentInfo> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(DhrNiCommentInfo::getHasConcentrate, concentrate);
            wrapper.in(DhrNiCommentInfo::getId, ids);
            wrapper.eq(DhrNiCommentInfo::getCommentStatus, DhrPtConstant.CommentStatus.SHTG);
            int rows = this.getBaseMapper().update(null, wrapper);
            Boolean result = rows > 0;
            if (result) {
                // 推送消息
                pushConcentrateMessage(request);
            }
            return result;
        }
        return false;
    }


    /**
     * 更新评论是否精选，推送消息通知
     *
     * @param request
     * @return
     */
    private Boolean pushConcentrateMessage(DhrNiCommentInfoConcentrateRequest request) {
        Boolean concentrate = request.getHasConcentrate();
        if (concentrate) {
            // 获取评论人列表
            List<DhrNiCommentInfoPushResponse> dhrNiCommentInfos = dhrNiCommentInfoMapper.findPushListByIds(request.getIds());
            List<DhrMiCommonMessageRequest> messageRequests = new ArrayList<>();
            for (DhrNiCommentInfoPushResponse pushResponse : dhrNiCommentInfos) {
                String title = pushResponse.getTitle();
                String commentContent = pushResponse.getCommentContent();
                String language = pushResponse.getLanguage();
                DhrMiCommonMessageRequest messageRequest = new DhrMiCommonMessageRequest();
                String message = MessageUtils.toLocaleSpecifyLanguage("dhr.ssc.comment.message.Concentrate", language, title, commentContent);
                messageRequest.setTitle(message);
                messageRequest.setMessage(message);
                messageRequest.setMessageType(DhrPtConstant.CommentMessage.TYPE);
                messageRequest.setSiteFilter(DhrPtConstant.CommentMessage.SITE_FILTER_IN);
                messageRequest.setMessageDate(new Date());
                messageRequest.setEmpCode(pushResponse.getCreateBy());
                messageRequest.setViewStatus(DhrPtConstant.CommentMessage.NOT_READ);
                messageRequests.add(messageRequest);
            }
            return miCommonMessageService.insertOrUpdateBatch(messageRequests);
        }
        return false;
    }

    @Override
    public void export(DhrNiCommentInfoRequest request) {
        List<DhrNiCommentInfoDTO> list = dhrNiCommentInfoMapper.findExportList(request, this.adjustOrder(null));
        // 转义
        escapeNiCommentInfo(list);
        // 执行导出到excel
        ExcelSheetData excelSheetData = new ExcelSheetData<>(DhrNiCommentInfoDTO.class, list);
        String fileName = MessageUtil.getText("export.name.dhr.comment") + "-" + DateUtil.format(new Date(), "yyyy-MM-dd") + ".xlsx";
        ExcelSheet excelSheet = new ExcelSheet("sheet", excelSheetData);
        excelParserTemplate.export(fileName, CollUtil.toList(excelSheet), Lists.newArrayList(new I18nCellWriteHandler()), null);
    }

    /**
     * 转义评论导出新
     *
     * @param list
     */
    private void escapeNiCommentInfo(List<DhrNiCommentInfoDTO> list) {
        if (CollUtil.isNotEmpty(list)) {
            List<Long> noticeIdList = list.stream().map(DhrNiCommentInfoDTO::getNoticeId).collect(Collectors.toList());
            LambdaQueryWrapper<NiOrgRel> orgRelWrapper = new LambdaQueryWrapper<>();
            orgRelWrapper.in(NiOrgRel::getNoticeId, noticeIdList);
            List<NiOrgRel> niOrgRelList = niOrgRelMapper.selectList(orgRelWrapper);
            Map<Long, List<NiOrgRel>> orgNiRelMap = niOrgRelList.stream().collect(Collectors.groupingBy(NiOrgRel::getNoticeId));
            List<String> niOrgCodeList = niOrgRelList.stream().map(NiOrgRel::getOrgCode).collect(Collectors.toList());
            // 获取组织名称
            Map<String, String> orgRelNameMap = sscFeignMethodUtil.findByOrgCodes(niOrgCodeList);

            List<String> empCodeList = list.stream().map(DhrNiCommentInfoDTO::getCreateBy).collect(Collectors.toList());
            Map<String, EmployeeBasicInfoResp> byEmpCodes = sscFeignMethodUtil.findByEmpCodes(empCodeList);
            list.forEach(item -> {
                // 填充适用组织信息
                List<NiOrgRel> niOrgRels = orgNiRelMap.get(item.getNoticeId());
                if (CollUtil.isNotEmpty(niOrgRels)) {
                    List<String> orgCodes = niOrgRels.stream().map(NiOrgRel::getOrgCode).collect(Collectors.toList());
                    List<String> orgNames = new ArrayList<>();
                    orgCodes.forEach(code -> {
                        String orgName = orgRelNameMap.get(code);
                        orgNames.add(orgName);
                    });
                    item.setNiOrgNames(orgNames);
                }
                // 创建人名称
                EmployeeBasicInfoResp employeeBasicInfoResp = byEmpCodes.get(item.getCreateBy());
                if (employeeBasicInfoResp != null) {
                    item.setCreateName(employeeBasicInfoResp.getFullname());
                }
                // 回复评论创建人名称
                EmployeeBasicInfoResp reEmployeeBasicInfoResp = byEmpCodes.get(item.getReplyCreateBy());
                if (reEmployeeBasicInfoResp != null) {
                    item.setReplyCreateName(reEmployeeBasicInfoResp.getFullname());
                }
            });
        }
    }
}

