<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dhr-ssc-service</artifactId>
        <groupId>com.deloitte.dhr</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>dhr-ssc-provider</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.sap</groupId>
            <artifactId>sapjco3</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/sapjco3.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-mda-api</artifactId>
            <exclusions>
                <!-- 排除易受攻击的Netty依赖 -->
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-handler</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-utility-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-common-mybtismvc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-common-auth</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
            <exclusions>
                <!-- 排除易受攻击的spring相关依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除易受攻击的Spring Security依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <!-- 排除易受攻击的Netty依赖 -->
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-handler</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <exclusions>
                <!-- 排除易受攻击的spring相关依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除易受攻击的Spring Security依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <!-- 排除易受攻击的Netty依赖 -->
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-handler</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>

        <!-- Circuit Breaker 依赖 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
        </dependency>

        <!-- Feign Hystrix 兼容性依赖 -->
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-hystrix</artifactId>
        </dependency>

        <!-- Spring Boot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
            <scope>compile</scope>
            <exclusions>
                <!-- 排除易受攻击的spring相关依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除易受攻击的Spring Security依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <exclusions>
                <!-- 排除易受攻击的Netty依赖 -->
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-handler</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <!-- 排除易受攻击的Netty依赖 -->
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-handler</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <!-- 排除易受攻击的spring相关依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除易受攻击的tomcat依赖 -->
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
            <exclusions>
                <!-- 排除易受攻击的spring相关依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.mysql</groupId>
                    <artifactId>mysql-connector-j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>${mysql-connector-j.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <exclusions>
                <!-- 排除易受攻击的spring相关依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <exclusions>
                <!-- 排除易受攻击的spring相关依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-excel-parser-starter</artifactId>
            <exclusions>
                <!-- 排除易受攻击的Spring Security依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <!-- 排除易受攻击的xmlunit依赖 -->
                <exclusion>
                    <groupId>org.xmlunit</groupId>
                    <artifactId>xmlunit-core</artifactId>
                </exclusion>
                <!-- 排除易受攻击的json-path依赖 -->
                <exclusion>
                    <groupId>com.jayway.jsonpath</groupId>
                    <artifactId>json-path</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 定时任务分布数锁-->
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-spring</artifactId>
            <exclusions>
                <!-- 排除易受攻击的spring相关依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-provider-redis-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-ssc-api</artifactId>
            <exclusions>
                <!-- 排除易受攻击的logback依赖 -->
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
            </exclusions>
        </dependency>

        <!--sentinel-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-actuator</artifactId>
            <exclusions>
                <!-- 排除易受攻击的spring相关依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除易受攻击的json-path依赖 -->
                <exclusion>
                    <groupId>com.jayway.jsonpath</groupId>
                    <artifactId>json-path</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
            <exclusions>
                <!-- 排除易受攻击的xmlunit依赖 -->
                <exclusion>
                    <groupId>org.xmlunit</groupId>
                    <artifactId>xmlunit-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- bpm -->
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>bpm-client</artifactId>
            <version>${revision}</version>
            <exclusions>
                <!-- 排除易受攻击的bouncycastle依赖 -->
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- selenium -->
        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-chrome-driver</artifactId>
            <version>${selenium.version}</version>
        </dependency>

        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-log-starter</artifactId>
            <exclusions>
                <!-- 排除易受攻击的logback依赖 -->
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-common-jco</artifactId>
        </dependency>
    </dependencies>

    <!-- 添加依赖管理来解决传递依赖的安全漏洞 -->
    <dependencyManagement>
        <dependencies>
            <!-- 解决 netty 安全漏洞 -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-http</artifactId>
                <version>${netty-codec-http.version}</version>
            </dependency>

            <!-- 解决 async-http-client 安全漏洞 -->
            <dependency>
                <groupId>org.asynchttpclient</groupId>
                <artifactId>async-http-client</artifactId>
                <version>${async.version}</version>
            </dependency>

            <!-- 解决 bouncycastle 安全漏洞 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15to18</artifactId>
                <version>${bcprov-jdk15to18.version}</version>
            </dependency>

            <!-- 解决 guava 安全漏洞 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- 解决 Spring 安全漏洞 -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring-web.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring-webmvc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring-beans.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring-context.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-expression</artifactId>
                <version>${spring-expression.version}</version>
            </dependency>

            <!-- 解决 Spring Security 安全漏洞 -->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${spring-security.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-web</artifactId>
                <version>${spring-security.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-crypto</artifactId>
                <version>${spring-security.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-config</artifactId>
                <version>${spring-security.version}</version>
            </dependency>

            <!-- 解决 Netty 安全漏洞 -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>${netty.version}</version>
            </dependency>

            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>${netty.version}</version>
            </dependency>

            <!-- 解决 Tomcat 安全漏洞 -->
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <!-- 解决 Logback 安全漏洞 -->
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <!-- 解决 xmlunit 安全漏洞 -->
            <dependency>
                <groupId>org.xmlunit</groupId>
                <artifactId>xmlunit-core</artifactId>
                <version>${xmlunit.version}</version>
            </dependency>

            <!-- 解决 json-path 安全漏洞 -->
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>${json-path.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>dhr-ssc-service</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!--开启过滤，用指定的参数替换directory下的文件中的参数-->
                <filtering>true</filtering>
            </resource>
            <!--引入sapjco3.jar-->
            <resource>
                <directory>${project.basedir}/src/main/resources/lib/</directory>
                <targetPath>BOOT-INF/lib</targetPath>
                <includes>
                    <include>**/*.jar</include>
                </includes>
            </resource>

            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>