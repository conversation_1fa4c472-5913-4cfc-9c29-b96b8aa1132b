<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.deloitte.dhr</groupId>
        <artifactId>dhr-service</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>dhr-gateway-service</artifactId>

    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
            <version>${spring-cloud-starter-gateway.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                <!-- 排除存在安全漏洞的spring-security-crypto传递依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <!-- 排除存在安全漏洞的spring-context传递依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的bouncycastle依赖 -->
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
                <!-- 排除存在安全漏洞的netty相关依赖 -->
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec-http</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-handler</artifactId>
                </exclusion>
                <!-- 排除存在安全漏洞的spring相关依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webflux</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-webmvc依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-expression依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-security-web依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <!-- 排除存在安全漏洞的spring-security-core依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <!-- 排除存在安全漏洞的spring-webmvc依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-expression依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-security-web依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <!-- 排除存在安全漏洞的spring-security-core依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <exclusions>
                <!-- 排除存在安全漏洞的spring-webmvc依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-expression依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-security-web依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <!-- 排除存在安全漏洞的spring-security-core依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
            <exclusions>
                <!-- 排除存在安全漏洞的spring-webmvc依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-expression依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-security-web依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <!-- 排除存在安全漏洞的spring-security-core依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <exclusions>
                <!-- 排除存在安全漏洞的spring-webmvc依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-expression依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-security-web依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <!-- 排除存在安全漏洞的spring-security-core依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15to18</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <exclusions>
                <!-- 排除存在安全漏洞的spring-webmvc依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-expression依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-security-web依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <!-- 排除存在安全漏洞的spring-security-core依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <!-- 排除存在安全漏洞的spring-webmvc依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-expression依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-security-web依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <!-- 排除存在安全漏洞的spring-security-core依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
            <exclusions>
                <!-- 排除存在安全漏洞的spring-webmvc依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-expression依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-security-web依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <!-- 排除存在安全漏洞的spring-security-core依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <!-- 排除存在安全漏洞的spring-webmvc依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-expression依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-security-web依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <!-- 排除存在安全漏洞的spring-security-core依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
            <exclusions>
                <!-- 排除存在安全漏洞的spring-webmvc依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-expression依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-security-web依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <!-- 排除存在安全漏洞的spring-security-core依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <!-- 排除存在安全漏洞的logback依赖 -->
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                <!-- 排除存在安全漏洞的spring-webmvc依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-expression依赖 -->
                <exclusion>
                    <groupId>org.springframework</groupId>
                <!-- 排除存在安全漏洞的spring-security-web依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <!-- 排除存在安全漏洞的spring-security-core依赖 -->
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>

        <!-- 添加兼容Spring Boot 3.2.4的logback依赖 -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
