<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.deloitte.dhr</groupId>
        <artifactId>dhr-bpm-service</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bpm-service</artifactId>

    <dependencies>
        <!-- 添加安全版本的logback依赖以解决安全漏洞 -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
        </dependency>
        
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>${logback.version}</version>
        </dependency>
        
        <!-- 添加安全版本的bouncycastle依赖以解决安全漏洞 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>${bcprov-jdk18on.version}</version>
        </dependency>

        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-spring-boot-starter-basic</artifactId>
            <!-- 排除存在安全漏洞的commons-email依赖 -->
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-email</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        
        <!-- 添加安全版本的commons-email以解决CVE-2017-9801和CVE-2018-1294漏洞 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-email</artifactId>
            <version>${commons-email.version}</version>
        </dependency>

        <!-- Mysql -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.mysql</groupId>
                    <artifactId>mysql-connector-j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>${mysql-connector-j.version}</version>
        </dependency>

        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
            <version>${spring-cloud-starter-loadbalancer.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--    nacos依赖开始    -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--    nacos依赖结束    -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <!-- 排除不安全的tomcat依赖 -->
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
            </exclusions>
        </dependency>
        
        <!-- 添加安全版本的tomcat依赖 -->
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>${tomcat.version}</version>
        </dependency>
        
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-websocket</artifactId>
            <version>${tomcat.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        
        <!-- 添加安全版本的spring-security-crypto依赖以解决CVE-2025-22228安全漏洞 -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
            <version>${spring-security.version}</version>
        </dependency>
        
        <!-- 添加安全版本的spring-web依赖 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${spring-web.version}</version>
        </dependency>
        
        <!-- 添加安全版本的spring-webmvc依赖 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${spring-webmvc.version}</version>
        </dependency>
        
        <!-- 添加安全版本的spring-expression依赖 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
            <version>${spring-expression.version}</version>
        </dependency>
        
        <!-- 添加安全版本的netty依赖以解决CVE-2024-47535等安全漏洞 -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-common</artifactId>
            <version>${netty.version}</version>
        </dependency>
        
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-handler</artifactId>
            <version>${netty.version}</version>
        </dependency>
        
        <!-- 添加安全版本的spring-security-web依赖以解决CVE-2024-38827安全漏洞 -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-web</artifactId>
            <version>${spring-security.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!--分页插件 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>pagehelper</artifactId>
                    <groupId>com.github.pagehelper</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.8.1</version>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>${pagehelper.version}</version>
        </dependency>


        <!--bean-->
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>bpm-bean</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>pagehelper</artifactId>
                    <groupId>com.github.pagehelper</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
        </dependency>


        <!-- dhr相关依赖 start   -->
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-mda-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>

        <!-- ssc -->
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-ssc-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-common-bean</artifactId>
        </dependency>
        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-common-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>

        <!-- performance-new -->
        <dependency>
            <artifactId>dhr-performance-new-api</artifactId>
            <groupId>com.deloitte.dhr</groupId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- performance -->
        <dependency>
            <artifactId>dhr-performance-api</artifactId>
            <groupId>com.deloitte.dhr</groupId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.deloitte.dhr</groupId>
            <artifactId>dhr-log-starter</artifactId>
        </dependency>
        
        <!-- 添加安全版本的spring相关依赖 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>${spring-beans.version}</version>
        </dependency>
        
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring-context.version}</version>
        </dependency>
        <!-- dhr相关依赖 end   -->
    </dependencies>


    <build>
        <finalName>dhr-bpm-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>